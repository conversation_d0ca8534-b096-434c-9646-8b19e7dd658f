<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图结构可视化</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .controls {
            padding: 15px;
            background: #ecf0f1;
            border-bottom: 1px solid #bdc3c7;
        }
        
        .control-group {
            display: inline-block;
            margin-right: 20px;
            margin-bottom: 10px;
        }
        
        .control-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }
        
        .control-group input, .control-group select {
            padding: 5px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }
        
        #graph-container {
            position: relative;
            width: 100%;
            height: 800px;
            overflow: hidden;
        }
        
        .node {
            cursor: pointer;
            stroke: #fff;
            stroke-width: 2px;
        }
        
        .node:hover {
            stroke-width: 3px;
        }
        
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
        }
        
        .node-label {
            font-size: 10px;
            font-family: Arial, sans-serif;
            text-anchor: middle;
            pointer-events: none;
            fill: #333;
        }
        
        .tooltip {
            position: absolute;
            padding: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 5px;
            pointer-events: none;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .legend {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ccc;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>图结构可视化</h1>
            <p>基于 meta_info_graph.json 的交互式图表</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>节点大小:</label>
                <input type="range" id="nodeSize" min="3" max="15" value="8">
            </div>
            <div class="control-group">
                <label>连接强度:</label>
                <input type="range" id="linkStrength" min="0.1" max="2" step="0.1" value="0.5">
            </div>
            <div class="control-group">
                <label>排斥力:</label>
                <input type="range" id="chargeStrength" min="-500" max="-50" value="-200">
            </div>
            <div class="control-group">
                <label>显示标签:</label>
                <input type="checkbox" id="showLabels" checked>
            </div>
            <div class="control-group">
                <label>过滤类型:</label>
                <select id="filterType">
                    <option value="">全部</option>
                </select>
            </div>
            <div class="control-group">
                <button id="resetZoom">重置视图</button>
            </div>
        </div>
        
        <div id="graph-container">
            <div class="legend" id="legend"></div>
        </div>
    </div>
    
    <div class="tooltip" id="tooltip" style="display: none;"></div>

    <script>
        // 图表配置
        const width = 1200;
        const height = 800;
        
        // 创建SVG
        const svg = d3.select("#graph-container")
            .append("svg")
            .attr("width", width)
            .attr("height", height);
            
        // 创建缩放组
        const g = svg.append("g");
        
        // 缩放行为
        const zoom = d3.zoom()
            .scaleExtent([0.1, 4])
            .on("zoom", (event) => {
                g.attr("transform", event.transform);
            });
            
        svg.call(zoom);
        
        // 颜色比例尺
        const colorScale = d3.scaleOrdinal(d3.schemeCategory10);
        
        // 力导向图模拟
        let simulation = d3.forceSimulation()
            .force("link", d3.forceLink().id(d => d.label_name).strength(0.5))
            .force("charge", d3.forceManyBody().strength(-200))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collision", d3.forceCollide().radius(15));
        
        // 全局变量
        let allNodes = [];
        let allLinks = [];
        let filteredNodes = [];
        let filteredLinks = [];
        let metaTypes = new Set();
        
        // 工具提示
        const tooltip = d3.select("#tooltip");
        
        // 加载数据
        d3.json("meta_info_graph.json").then(function(data) {
            console.log("数据加载成功", data);
            
            // 处理数据
            allNodes = data.nodes.map(d => ({
                ...d,
                id: d.label_name,
                x: Math.random() * width,
                y: Math.random() * height
            }));
            
            allLinks = data.edges.map(d => ({
                ...d,
                source: d.source,
                target: d.target
            }));
            
            // 收集所有元数据类型
            allNodes.forEach(d => metaTypes.add(d.meta_info_type));
            
            // 初始化过滤器
            initializeFilters();
            
            // 初始化图表
            filteredNodes = [...allNodes];
            filteredLinks = [...allLinks];
            updateGraph();
            
            // 创建图例
            createLegend();
        }).catch(function(error) {
            console.error("数据加载失败:", error);
            alert("数据加载失败，请确保 meta_info_graph.json 文件在同一目录下");
        });
        
        function initializeFilters() {
            const filterSelect = d3.select("#filterType");
            metaTypes.forEach(type => {
                filterSelect.append("option")
                    .attr("value", type)
                    .text(type);
            });
        }
        
        function createLegend() {
            const legend = d3.select("#legend");
            const legendData = Array.from(metaTypes).slice(0, 10); // 只显示前10个类型
            
            legend.selectAll(".legend-item")
                .data(legendData)
                .enter()
                .append("div")
                .attr("class", "legend-item")
                .html(d => `
                    <div class="legend-color" style="background-color: ${colorScale(d)}"></div>
                    <span>${d}</span>
                `);
        }
        
        function updateGraph() {
            // 清除现有元素
            g.selectAll("*").remove();
            
            // 创建连接线
            const link = g.append("g")
                .attr("class", "links")
                .selectAll("line")
                .data(filteredLinks)
                .enter().append("line")
                .attr("class", "link")
                .attr("stroke-width", d => Math.sqrt(d.weight || 1));
            
            // 创建节点
            const node = g.append("g")
                .attr("class", "nodes")
                .selectAll("circle")
                .data(filteredNodes)
                .enter().append("circle")
                .attr("class", "node")
                .attr("r", d => Math.sqrt(d.degree || 10) * 0.5 + 5)
                .attr("fill", d => colorScale(d.meta_info_type))
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended))
                .on("mouseover", showTooltip)
                .on("mouseout", hideTooltip)
                .on("click", highlightConnections);
            
            // 创建标签
            const labels = g.append("g")
                .attr("class", "labels")
                .selectAll("text")
                .data(filteredNodes)
                .enter().append("text")
                .attr("class", "node-label")
                .text(d => {
                    // 截断长标签
                    const maxLength = 20;
                    return d.label_name.length > maxLength ? 
                           d.label_name.substring(0, maxLength) + "..." : 
                           d.label_name;
                })
                .style("display", d3.select("#showLabels").property("checked") ? "block" : "none");
            
            // 更新模拟
            simulation.nodes(filteredNodes);
            simulation.force("link").links(filteredLinks);
            simulation.alpha(1).restart();
            
            // 模拟tick事件
            simulation.on("tick", () => {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                
                labels
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + 4);
            });
        }
        
        function showTooltip(event, d) {
            tooltip
                .style("display", "block")
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 10) + "px")
                .html(`
                    <strong>${d.label_name}</strong><br>
                    类型: ${d.meta_info_type}<br>
                    共现次数: ${d.co_occurrence_count}<br>
                    度数: ${d.degree}
                `);
        }
        
        function hideTooltip() {
            tooltip.style("display", "none");
        }
        
        function highlightConnections(event, d) {
            // 重置所有样式
            g.selectAll(".node").style("opacity", 0.3);
            g.selectAll(".link").style("opacity", 0.1);
            
            // 高亮选中节点
            d3.select(event.currentTarget).style("opacity", 1);
            
            // 高亮相连的节点和边
            g.selectAll(".link")
                .filter(link => link.source === d || link.target === d)
                .style("opacity", 1)
                .each(function(link) {
                    g.selectAll(".node")
                        .filter(node => node === link.source || node === link.target)
                        .style("opacity", 1);
                });
        }
        
        // 拖拽函数
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
        
        // 控制面板事件监听
        d3.select("#nodeSize").on("input", function() {
            const size = +this.value;
            g.selectAll(".node").attr("r", d => Math.sqrt(d.degree || 10) * (size/8) * 0.5 + size/2);
        });
        
        d3.select("#linkStrength").on("input", function() {
            simulation.force("link").strength(+this.value);
            simulation.alpha(1).restart();
        });
        
        d3.select("#chargeStrength").on("input", function() {
            simulation.force("charge").strength(+this.value);
            simulation.alpha(1).restart();
        });
        
        d3.select("#showLabels").on("change", function() {
            g.selectAll(".node-label").style("display", this.checked ? "block" : "none");
        });
        
        d3.select("#filterType").on("change", function() {
            const selectedType = this.value;
            if (selectedType === "") {
                filteredNodes = [...allNodes];
                filteredLinks = [...allLinks];
            } else {
                filteredNodes = allNodes.filter(d => d.meta_info_type === selectedType);
                const nodeIds = new Set(filteredNodes.map(d => d.label_name));
                filteredLinks = allLinks.filter(d => nodeIds.has(d.source) && nodeIds.has(d.target));
            }
            updateGraph();
        });
        
        d3.select("#resetZoom").on("click", function() {
            svg.transition().duration(750).call(
                zoom.transform,
                d3.zoomIdentity
            );
            // 重置高亮
            g.selectAll(".node").style("opacity", 1);
            g.selectAll(".link").style("opacity", 0.6);
        });
        
        // 双击背景重置高亮
        svg.on("dblclick.zoom", null).on("dblclick", function() {
            g.selectAll(".node").style("opacity", 1);
            g.selectAll(".link").style("opacity", 0.6);
        });
    </script>
</body>
</html>
